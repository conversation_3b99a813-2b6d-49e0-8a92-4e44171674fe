import { forwardRef, useEffect, useRef } from 'react'
import cn from '@/utils/classnames'

type AutoHeightTextareaProps = {
  outerClassName?: string
  value?: string
  className?: string
  placeholder?: string
  autoFocus?: boolean
  disabled?: boolean
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void
} & Omit<React.HTMLAttributes<HTMLDivElement>, 'onChange'>

const AutoHeightTextarea = forwardRef<HTMLDivElement, AutoHeightTextareaProps>(
  (
    {
      outerClassName,
      value,
      className,
      placeholder,
      autoFocus,
      disabled,
      onChange,
      ...rest
    },
    outRef,
  ) => {
    const innerRef = useRef<HTMLDivElement>(null)
    const ref = outRef || innerRef

    useEffect(() => {
      if (autoFocus && !disabled && value) {
        if (typeof ref !== 'function') {
          ref.current?.focus()
        }
      }
    }, [autoFocus, disabled, ref])

    const handleInput = (e: React.FormEvent<HTMLDivElement>) => {
      if (onChange) {
        const target = e.target as HTMLDivElement
        onChange({
          target: {
            value: target.innerText,
          },
        } as React.ChangeEvent<HTMLTextAreaElement>)
      }
    }

    return (
      <div className={outerClassName}>
        <div className='relative flex flex-wrap'>
          <div className={cn(className, 'invisible whitespace-normal')}>
            {!value ? placeholder : `${value}`.replace(/\n$/, '\n ')}
          </div>
          <div
            ref={ref as any}
            contentEditable={!disabled}
            className={cn(className, 'disabled:bg-transparent absolute inset-0 outline-none border-none appearance-none resize-none w-full h-full whitespace-normal', {
              'bg-[#1b1b1b]': !disabled
            })}
            onInput={handleInput}
            dangerouslySetInnerHTML={{ __html: value || '' }}
            data-placeholder={placeholder}
            {...rest}
          />
        </div>
      </div>
    )
  },
)

export default AutoHeightTextarea
