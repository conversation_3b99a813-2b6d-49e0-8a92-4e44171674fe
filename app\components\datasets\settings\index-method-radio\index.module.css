.icon {
  margin-right: 12px;
  width: 24px;
  height: 24px;
  background:  center center no-repeat;
  background-size: contain;
}

.high-quality-icon {
  background-image: url(./assets/high-quality.svg);
}
.economy-icon {
  background-image: url(./assets/economy.svg);
}

.wrapper .item:hover {
  background-color: #000;
  border-color: #1d1f21;
  box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);
}

.wrapper .item-active {
  background-color: #000;
  border-width: 1.5px;
  border-color: #0BA581;
  box-shadow: 0px 1px 3px rgba(16, 24, 40, 0.1), 0px 1px 2px rgba(16, 24, 40, 0.06);
}

.wrapper .item-active .radio {
  border-width: 5px;
  border-color: #0BA581;
}

.wrapper .item-active:hover {
  border-width: 1.5px;
  border-color: #0BA581;
  box-shadow: 0px 1px 3px rgba(16, 24, 40, 0.1), 0px 1px 2px rgba(16, 24, 40, 0.06);
}

.wrapper .item.disable {
  @apply opacity-60;
}
.wrapper .item-active.disable {
  @apply opacity-60;
}
.wrapper .item.disable:hover {
  @apply bg-black border border-gray-700 shadow-none cursor-default opacity-60;
}
.wrapper .item-active.disable:hover {
  @apply cursor-default opacity-60;
  border-width: 1.5px;
  border-color: #0BA581;
  box-shadow: 0px 1px 3px rgba(16, 24, 40, 0.1), 0px 1px 2px rgba(16, 24, 40, 0.06);
}
