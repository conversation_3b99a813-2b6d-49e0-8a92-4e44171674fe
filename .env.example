# # For production release, change this to PRODUCTION
# NEXT_PUBLIC_DEPLOY_ENV=DEVELOPMENT
# # The deployment edition, SELF_HOSTED
# NEXT_PUBLIC_EDITION=SELF_HOSTED
# # The base URL of console application, refers to the Console base URL of WEB service if console domain is
# # different from api or web app domain.
# # example: http://cloud.dify.ai/console/api
# NEXT_PUBLIC_API_PREFIX=http://localhost:5001/console/api
# # The URL for Web APP, refers to the Web App base URL of WEB service if web app domain is different from
# # console or api domain.
# # example: http://udify.app/api
# NEXT_PUBLIC_PUBLIC_API_PREFIX=http://localhost:5001/api

# # SENTRY
# NEXT_PUBLIC_SENTRY_DSN=

# # Disable Next.js Telemetry (https://nextjs.org/telemetry)
# NEXT_TELEMETRY_DISABLED=1

# # Disable Upload Image as WebApp icon default is false
# NEXT_PUBLIC_UPLOAD_IMAGE_AS_ICON=false

# # The timeout for the text generation in millisecond
# NEXT_PUBLIC_TEXT_GENERATION_TIMEOUT_MS=60000

# # CSP https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP
# NEXT_PUBLIC_CSP_WHITELIST=
DIFY_APP_URL=http://localhost:3000
ZHIHUI_API_URL=http://**************:35001//console/api
API=http://**************:35001/
ZHIHUI_WEB_URL=http://127.0.0.1:3000